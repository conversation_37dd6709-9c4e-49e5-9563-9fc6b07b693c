/*
// 使用示例
import api from '@/api'

// 用户登录
api.user.login({ username: 'your_username', password: 'your_password' })
  .then(userInfo => {
    console.log('登录成功', userInfo)
  })
  .catch(error => {
    console.error('登录失败', error)
  })

// 获取物品列表
api.item.getItemList()
  .then(list => {
    console.log('获取列表成功', list)
  })
*/


import * as user from './user'
import * as item from './item'

export default {
  user,
  item
}
