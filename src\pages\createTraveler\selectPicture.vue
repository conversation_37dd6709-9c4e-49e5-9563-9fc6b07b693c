<!-- 选择旅行家头像 -->
<template>
  <NavBar title="选择旅行家" />
  <button class="primary_button flex_c_c" @click="handleUpdate">测试选择图片</button>
  <button class="primary_button flex_c_c" @click="handleCreate">去旅行</button>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { onLoad } from '@dcloudio/uni-app'
import { reactive } from 'vue'
import { post } from '@/utils/request.js'

const pageData = reactive({
  pet_type: '',
  pet_name: '',
  img_url: 'https://oss-file.helloufu.com/logo/100170/<EMAIL>'
})

const handleUpdate = async () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original'],
    sourceType: ['camera'],
    success: async res => {
      console.log('图片路径为:', res)
      // 暂时不通忽略
      const data = await post('?c=Pet&m=upload_img', {
        filePath: res.tempFilePaths[0]
      })
    }
  })
}

// 创建萌宠并跳转下一页
const handleCreate = async () => {
  try {
    const data = await post('?c=Pet&m=pet_modify', pageData)
  } catch {}
}

onLoad(options => {
  console.log(options)
  pageData.pet_type = options.pet_type
  pageData.pet_name = options.pet_name
})
</script>
