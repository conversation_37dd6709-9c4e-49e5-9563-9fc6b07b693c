<template>
  <view class="page-container" :style="{ paddingTop: pageData.navBarHeight + 'px' }">
    <NavBar title="选择照片" @update:navBarHeight="height => (pageData.navBarHeight = height)" />

    <!-- 背景 -->
    <view class="bg"></view>

    <!-- 主要内容 -->
    <view class="content">
      <!-- 照片选择区域 -->
      <view class="photo-select-area" @click="handleSelectPhoto">
        <view class="photo-container">
          <image
            v-if="pageData.selectedImage"
            :src="pageData.selectedImage"
            class="selected-photo"
            mode="aspectFill"
          />
          <view v-else class="photo-placeholder">
            <!-- 相机图标组合 -->
            <view class="camera-icon-container">
              <!-- 相机主体 -->
              <view class="camera-main">
                <!-- 相机镜头外圈 -->
                <view class="camera-lens-outer">
                  <!-- 相机镜头内圈 -->
                  <view class="camera-lens-inner"></view>
                </view>
                <!-- 相机闪光灯 -->
                <view class="camera-flash"></view>
                <!-- 相机取景器 -->
                <view class="camera-viewfinder"></view>
              </view>
            </view>
            <text class="add-text">点击添加</text>
          </view>
        </view>
      </view>

      <!-- 提示文字 -->
      <view class="tip-text">
        <text>请选择一张你家宝贝清晰的单人照</text>
        <text>它将作为旅行的"护照证件照"</text>
      </view>

      <!-- 去旅行按钮 -->
      <view class="travel-button" @click="handleCreate">
        <text class="travel-button-text">去旅行</text>
      </view>
    </view>

    <!-- 底部装饰条 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { onLoad } from '@dcloudio/uni-app'
import { reactive } from 'vue'
import { post } from '@/utils/request.js'

const pageData = reactive({
  navBarHeight: 0,
  pet_type: '',
  pet_name: '',
  selectedImage: '', // 选中的图片路径
  img_url: ''
})

// 选择照片
const handleSelectPhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: async res => {
      console.log('选择的图片:', res)
      pageData.selectedImage = res.tempFilePaths[0]

      // 上传图片
      try {
        const data = await post('?c=Pet&m=upload_img', {
          filePath: res.tempFilePaths[0]
        })
        pageData.img_url = data.url || data.img_url
        console.log('图片上传成功:', data)
      } catch (error) {
        console.error('图片上传失败:', error)
        uni.showToast({
          title: '图片上传失败',
          icon: 'none'
        })
      }
    },
    fail: error => {
      console.error('选择图片失败:', error)
    }
  })
}

// 创建萌宠并跳转下一页
const handleCreate = async () => {
  if (!pageData.selectedImage && !pageData.img_url) {
    uni.showToast({
      title: '请先选择照片',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '创建中...'
    })

    const data = await post('?c=Pet&m=pet_modify', {
      pet_type: pageData.pet_type,
      pet_name: pageData.pet_name,
      img_url: pageData.img_url
    })

    uni.hideLoading()

    // 保存宠物信息到本地存储
    uni.setStorageSync('petInfo', data)

    // 跳转到首页或下一页
    uni.redirectTo({
      url: '/pages/index/index'
    })
  } catch (error) {
    uni.hideLoading()
    console.error('创建失败:', error)
    uni.showToast({
      title: '创建失败，请重试',
      icon: 'none'
    })
  }
}

onLoad(options => {
  console.log('页面参数:', options)
  if (options) {
    pageData.pet_type = options.pet_type || ''
    pageData.pet_name = options.pet_name || ''
  }
})
</script>

<style lang="scss" scoped>
.page-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.bg {
  position: absolute;
  top: 352rpx; // 176px * 2 = 352rpx (导航栏高度)
  left: 0;
  width: 1500rpx; // 750px * 2
  height: 2896rpx; // 1448px * 2
  background: linear-gradient(180deg, #e2fbb3 0%, rgba(226, 251, 179, 0) 100%);
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 照片选择区域 */
.photo-select-area {
  margin-top: 304rpx; // 根据 Figma Y坐标 480rpx - 176rpx(导航栏) = 304rpx
  width: 960rpx; // 480px * 2
  height: 960rpx; // 480px * 2
  position: relative;
}

.photo-container {
  width: 100%;
  height: 100%;
  background: #fefbf4;
  border: 32rpx solid #ffffff; // 16px * 2
  border-radius: 80rpx; // 40px * 2
  box-shadow: 0px 24rpx 96rpx 0px rgba(29, 33, 41, 0.03), 0px 18rpx 56rpx 0px rgba(29, 33, 41, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.photo-container:active {
  transform: scale(0.98);
  box-shadow: 0px 12rpx 48rpx 0px rgba(29, 33, 41, 0.05), 0px 9rpx 28rpx 0px rgba(29, 33, 41, 0.08);
}

.selected-photo {
  width: 100%;
  height: 100%;
  border-radius: 48rpx; // 内部圆角稍小
}

.photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 相机图标 */
.camera-icon-container {
  width: 256rpx; // 128px * 2
  height: 342rpx; // 171px * 2
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.camera-main {
  width: 180rpx; // 90px * 2
  height: 164rpx; // 82px * 2
  background: #000000;
  border-radius: 25.714rpx; // 12.857px * 2
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-lens-outer {
  width: 102.86rpx; // 51.43px * 2
  height: 100.92rpx; // 50.46px * 2
  background: #ffffff;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-lens-inner {
  width: 64.28rpx; // 32.14px * 2
  height: 63.08rpx; // 31.54px * 2
  background: #000000;
  border-radius: 50%;
}

.camera-flash {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  width: 19.28rpx; // 9.64px * 2
  height: 18.92rpx; // 9.46px * 2
  background: #ffffff;
  border-radius: 50%;
}

.camera-viewfinder {
  position: absolute;
  top: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 77.14rpx; // 38.57px * 2
  height: 25.24rpx; // 12.62px * 2
  background: #ffffff;
  border-radius: 6rpx;
}

.add-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 64rpx; // 32px * 2
  font-weight: 600;
  color: #b4b7ae;
  text-align: center;
}

/* 提示文字 */
.tip-text {
  margin-top: 120rpx; // 根据设计稿调整间距
  width: 840rpx; // 420px * 2
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.tip-text text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 56rpx; // 28px * 2
  font-weight: 500;
  color: #1e1e1e;
  line-height: 1.5;
}

/* 去旅行按钮 */
.travel-button {
  margin-top: 180rpx; // 根据设计稿调整间距
  width: 1240rpx; // 620px * 2
  height: 232rpx; // 116px * 2
  background: #1e1e1e;
  border-radius: 116rpx; // 58px * 2
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.travel-button:active {
  transform: scale(0.98);
  background: #333333;
}

.travel-button-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 64rpx; // 32px * 2
  font-weight: 600;
  color: #ffffff;
}

/* 底部装饰条 */
.bottom-decoration {
  position: absolute;
  bottom: 52rpx; // 26rpx from bottom
  left: 50%;
  transform: translateX(-50%);
  width: 536rpx; // 268px * 2
  height: 20rpx; // 10px * 2
  background: #000000;
  border-radius: 10rpx; // 5px * 2
  z-index: 3;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .photo-select-area {
    width: 80vw;
    height: 80vw;
    max-width: 960rpx;
    max-height: 960rpx;
  }

  .tip-text {
    width: 90vw;
    max-width: 840rpx;
  }

  .travel-button {
    width: 90vw;
    max-width: 1240rpx;
  }
}
</style>
