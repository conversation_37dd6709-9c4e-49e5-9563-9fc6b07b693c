<template>
  <view class="page-container" :style="{ paddingTop: pageData.navBarHeight + 'px' }">
    <NavBar title="照片裁剪" @update:navBarHeight="height => pageData.navBarHeight = height" />
    <view class="content">
      <view class="cropper-wrapper">
        <image class="cropper-image" src="@/static/images/crop-image-placeholder.png" mode="aspectFit"></image>
        <view class="cropper-box"></view>
      </view>
      <view class="button-group">
        <text class="cancel-button">取消</text>
        <text class="confirm-button">确定</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive } from 'vue';
import NavBar from '@/components/NavBar.vue';

const pageData = reactive({
  navBarHeight: 0,
});
</script>

<style lang="scss" scoped>
.page-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 750rpx;
  height: 1624rpx;
  background: #000000;

  .content {
    position: absolute;
    left: 0;
    top: 176rpx;
    width: 750rpx;
    height: 1448rpx;
    background: #000000;

    .cropper-wrapper {
      position: absolute;
      left: 0;
      top: 296rpx;
      width: 750rpx;
      height: 750rpx;

      .cropper-image {
        position: absolute;
        left: 0;
        top: 0;
        width: 750rpx;
        height: 750rpx;
      }

      .cropper-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 750rpx;
        height: 750rpx;
        border: 1px solid #FFFFFF;
      }
    }

    .button-group {
      position: absolute;
      left: 48rpx;
      top: 1354rpx;
      width: 654rpx;
      height: 44rpx;
      display: flex;
      justify-content: space-between;

      .cancel-button {
        font-size: 32rpx;
        font-family: PingFang SC, sans-serif;
        font-weight: normal;
        line-height: 44rpx;
        color: #FFFFFF;
      }

      .confirm-button {
        font-size: 32rpx;
        font-family: PingFang SC, sans-serif;
        font-weight: normal;
        line-height: 44rpx;
        color: #FFFFFF;
      }
    }
  }
}
</style>