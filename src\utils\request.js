/**
 * 网络请求工具
 */

// 基础配置
const BASE_URL = 'https://xiangtuikeji.com/cp/index.php'
const TIMEOUT = 10000

/**
 * 请求拦截器
 */
function requestInterceptor(config) {
  // 添加基础URL
  config.url = `${BASE_URL}${config.url}`

  // 添加默认header
  config.header = {
    'Content-Type': 'application/x-www-form-urlencoded',
    ...config.header
  }

  // 全局接口中添加token
  config.data = {
    ...config.data,
    token: uni.getStorageSync('token') || ''
  }

  console.log('Request:', config)
  return config
}

/**
 * 响应拦截器
 */
function responseInterceptor(response) {
  console.log('Response:', response)

  const { statusCode, data } = response

  // HTTP状态码检查
  if (statusCode !== 200) {
    return Promise.reject(new Error(`HTTP Error: ${statusCode}`))
  }

  // 业务状态码检查
  if (data.returncode !== '0') {
    uni.showToast({
      title: data.returnmsg || '请求失败',
      icon: 'none'
    })
    return Promise.reject(new Error(data.returnmsg || '请求失败'))
  }

  return Promise.resolve(data.data)
}

/**
 * 通用请求方法
 */
function request(config) {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const finalConfig = requestInterceptor(config)

    uni.request({
      url: finalConfig.url,
      method: finalConfig.method || 'GET',
      data: finalConfig.data,
      header: finalConfig.header,
      timeout: finalConfig.timeout || TIMEOUT,
      success: response => {
        responseInterceptor(response).then(resolve).catch(reject)
      },
      fail: error => {
        console.error('Request failed:', error)
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

/**
 * GET请求
 */
export function get(url, data, header) {
  return request({
    url,
    method: 'GET',
    data,
    header
  })
}

/**
 * POST请求
 */
export function post(url, data, header) {
  return request({
    url,
    method: 'POST',
    data,
    header
  })
}

/**
 * PUT请求
 */
export function put(url, data, header) {
  return request({
    url,
    method: 'PUT',
    data,
    header
  })
}

/**
 * DELETE请求
 */
export function del(url, data, header) {
  return request({
    url,
    method: 'DELETE',
    data,
    header
  })
}

// 默认导出
export default {
  get,
  post,
  put,
  delete: del
}
