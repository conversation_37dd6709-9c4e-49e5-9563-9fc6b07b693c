<!-- 选择旅行家 -->
<template>
  <view class="container" :style="{ paddingTop: pageData.navBarHeight + 'px' }">
    <NavBar title="选择旅行家" @update:navBarHeight="height => (pageData.navBarHeight = height)" />
    <view class="title">Hi～</view>
    <view class="sub-title">请问你的旅行家是？</view>
    <view class="card-container">
      <view
        :class="['card', pageData.pet_type === '1' ? 'active' : '']"
        @click="() => (pageData.pet_type = '1')"
      >
        <image class="card-image" src="@/static/createTraveler/pet.png"></image>
        <image
          v-if="pageData.pet_type === '1'"
          class="card-active-icon"
          src="@/static/createTraveler/active.png"
        />
        <view class="card-title">可爱的宠物</view>
        <view class="card-desc">猫咪、狗狗...</view>
      </view>
      <view
        :class="['card', pageData.pet_type === '2' ? 'active' : '']"
        @click="() => (pageData.pet_type = '2')"
      >
        <image class="card-image" src="@/static/createTraveler/baby.png" />
        <image
          v-if="pageData.pet_type === '2'"
          class="card-active-icon"
          src="@/static/createTraveler/active.png"
        />
        <view class="card-title">可爱的宝贝</view>
        <view class="card-desc">玩偶、手办…</view>
      </view>
    </view>
    <view class="input-title">请问你的旅行家叫？</view>
    <input class="input" placeholder="输入旅行家名字" v-model="pageData.pet_name" />
    <button
      class="primary_button flex_c_c"
      :class="{ disabled: btnIsDisabled }"
      @click="handleToNext"
    >
      下一步
    </button>
  </view>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { reactive, computed } from 'vue'

const pageData = reactive({
  navBarHeight: 0,
  pet_type: '',
  pet_name: ''
})

const btnIsDisabled = computed(() => {
  return !pageData.pet_type || !pageData.pet_name
})

const handleToNext = async () => {
  console.log(btnIsDisabled.value)
  if (btnIsDisabled.value) return
  uni.navigateTo({
    url: `/pages/createTraveler/selectPicture?pet_type=${pageData.pet_type}&pet_name=${pageData.pet_name}`
  })
}
</script>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #e2fbb3 0%, rgba(226, 251, 179, 0) 100%);
  position: relative;
  box-sizing: border-box;
}
.title {
  margin-left: 65rpx;
  font-size: 60rpx;
  font-weight: 600;
  color: #1e1e1e;
}
.sub-title {
  margin-top: 16rpx;
  margin-left: 65rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #1e1e1e;
}
.card-container {
  margin-top: 36rpx;
  padding-left: 65rpx;
  padding-right: 65rpx;
  display: flex;
  justify-content: space-between;
}
.card {
  width: 280rpx;
  height: 260rpx;
  margin-top: 100rpx;
  background: #ffffff;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: 8rpx solid transparent;
  box-sizing: border-box;
}
.card.active {
  border: 8rpx solid #b9e085;
}
.card-image {
  width: 200rpx;
  height: 200rpx;
  position: absolute;
  top: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}
.card-active-icon {
  position: absolute;
  top: 14rpx;
  right: 40rpx;
  width: 86rpx;
  height: 86rpx;
  z-index: 3;
}
.card .card-title {
  margin-top: 116rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e1e1e;
  margin-top: 16rpx;
}
.card-desc {
  font-size: 28rpx;
  font-weight: 600;
  color: #a4a3b0;
  margin-top: 12rpx;
}
.input-title {
  margin-top: 144rpx;
  margin-left: 65rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #1e1e1e;
}
.input {
  margin-top: 36rpx;
  margin-left: 65rpx;
  width: 620rpx;
  height: 116rpx;
  background: #ffffff;
  border-radius: 58rpx;
  text-align: center;
  font-size: 32rpx;
}
.primary_button {
  position: absolute;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
