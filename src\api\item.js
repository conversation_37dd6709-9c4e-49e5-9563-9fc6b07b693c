import { get, post, put, del } from '@/utils/request.js'

/**
 * 获取物品列表
 * @param {object} params - 查询参数
 */
export function getItemList(params) {
  return get('/api/items', params)
}

/**
 * 获取物品详情
 * @param {string} id - 物品ID
 */
export function getItemDetail(id) {
  return get(`/api/items/${id}`)
}

/**
 * 创建新物品
 * @param {object} data - 物品数据
 */
export function createItem(data) {
  return post('/api/items', data)
}

/**
 * 更新物品信息
 * @param {string} id - 物品ID
 * @param {object} data - 更新的物品数据
 */
export function updateItem(id, data) {
  return put(`/api/items/${id}`, data)
}

/**
 * 删除物品
 * @param {string} id - 物品ID
 */
export function deleteItem(id) {
  return del(`/api/items/${id}`)
}