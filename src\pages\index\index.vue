<template>
  <view class="pages" :style="{ paddingTop: pageData.navBarHeight + 'px' }">
    <NavBar title="萌物旅行" @update:navBarHeight="height => (pageData.navBarHeight = height)" />
    <view @click="handleToCreate">测试跳转</view>
    <view class="mt-20" style="padding-top: 100rpx">
      <view>{{ pageData.navBarHeight }}</view>
    </view>
  </view>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { onMounted, reactive } from 'vue'
import { post } from '@/utils/request.js'

const pageData = reactive({
  navBarHeight: 0
})

const handleToCreate = () => {
  console.log(89899)
  uni.redirectTo({ url: '/pages/createTraveler/welcome' })
}

// 优先获取萌宠信息，已经创建过跳转首页，未创建跳转创建页
const getPetInfo = async () => {
  try {
    const data = await post('?c=Pet&m=pet_info')
    wx.setStorageSync('petInfo', data)
    // if (data.id) {
    //   uni.redirectTo({ url: '/pages/index/index' })
    // } else {
    //   uni.redirectTo({ url: '/pages/createTraveler/welcome' })
    // }
  } catch {}
}

onMounted(() => {
  // getPetInfo()
})
</script>

<style scoped>
.pages {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
}
</style>
