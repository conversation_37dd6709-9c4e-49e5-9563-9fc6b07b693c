{"version": "0.2.0", "configurations": [{"name": "Debug H5", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile"}, {"name": "Debug 微信小程序", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@dcloudio/uni-cli-shared/bin/uniapp-cli.js", "args": ["--platform", "mp-weixin"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}