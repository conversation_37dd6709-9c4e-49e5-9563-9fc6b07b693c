<template>
  <view class="navbar" :style="navBarStyle">
    <view class="navbar-wrapper">
      <view class="navbar-left">
        <slot name="left">
          <view class="icon-left-wrapper" @click="goBack">
            <uni-icons type="left" size="24"></uni-icons>
          </view>
        </slot>
      </view>
      <view class="navbar-center">
        <slot>
          <text class="title">{{ title }}</text>
        </slot>
      </view>
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, computed, onMounted, defineEmits } from 'vue'

const emit = defineEmits(['update:navBarHeight'])

const windowInfo = uni.getWindowInfo()
const statusBarHeight = windowInfo.statusBarHeight || 0
const navBarContentHeight = 44
const navBarHeight = statusBarHeight + navBarContentHeight

const navBarStyle = computed(() => ({
  height: `${navBarHeight}px`,
  paddingTop: `${statusBarHeight}px`
}))

onMounted(() => {
  emit('update:navBarHeight', navBarHeight)
})

defineProps({
  title: {
    type: String,
    default: ''
  }
})

const goBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  box-sizing: border-box;
  background-color: #e2fbb3;

  .navbar-wrapper {
    display: flex;
    align-items: center;
    height: 44px;
    padding: 0 15px;
    position: relative;
  }

  .navbar-left {
    display: flex;
    align-items: center;
  }

  .icon-left-wrapper {
    display: flex;
    align-items: center;
    padding-right: 10px;
  }

  .navbar-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;

    .title {
      font-size: 17px;
      font-weight: 500;
    }
  }

  .navbar-right {
    margin-left: auto;
  }
}
</style>
