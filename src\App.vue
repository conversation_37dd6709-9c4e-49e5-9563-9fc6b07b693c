<script setup>
import { post } from '@/utils/request.js'

// wx.login({
//   success: async res => {
//     if (res.code) {
//       const data = await post('?c=Login&m=wx_login', { code: res.code })
//       console.log(data, '登录成功')
//       wx.setStorageSync('token', data.token)
//       wx.setStorageSync('openid', data.openid)
//       wx.setStorageSync('userInfo', data)
//     } else {
//       console.log('登录失败！' + res.errMsg)
//     }
//   }
// })
</script>
<style>
.flex_c_c {
  display: flex;
  align-items: center;
  justify-content: center;
}
.primary_button {
  width: 620rpx;
  height: 116rpx;
  border-radius: 100rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  background: #1e1e1e;
}
.primary_button.disabled {
  background: #b4b7ae;
}
</style>
