2025 年 8 月 7 日
你好，请根据 Figma 设计稿 完成【创建旅行家】 页面的开发，具体要求如下：

1.  **目标文件**：请在 文件中实现。
2.  **设计节点**：请重点分析节点 ID 【Figma 节点 ID】 的内容。
3.  **开发要求**： - 所有布局和样式请使用 rpx 单位，确保像素级还原。 - 所有元素的垂直定位，使用 Figma Y 坐标，单位为 rpx 。 - 请先将设计稿中的图片资源下载到 【指定的静态资源目录】 下，然后再进行引用。 - 导航栏高度单位使用 px，其余使用 rpx - 导航栏使用方式如下

```vue
    <view class="page-container" :style="{ paddingTop: pageData.navBarHeight + 'px' }">
      <NavBar title="页面标题" @update:navBarHeight="height => pageData.navBarHeight = height" />
      <view class="content">
        <text>这里是页面的主要内容</text>
      </view>
    </view>
```
