<template>
  <view class="page" :style="{ '--nav-bar-height': pageData.navBarHeight + 'px' }">
    <NavBar title="萌物旅行" @update:navBarHeight="height => (pageData.navBarHeight = height)" />
    <image class="bg" src="/static/createTraveler/bg.png" mode="aspectFill"></image>
    <view class="content">
      <view class="title">欢迎来到萌物旅行</view>
      <view class="sub-title">
        <view>让心爱的Ta去旅行</view>
        <view>每天给你邮寄明信片</view>
      </view>
      <view class="primary_button flex_c_c" @click="handleToCreate">去创建</view>
    </view>
  </view>
</template>

<script setup>
import NavBar from '@/components/NavBar.vue'
import { reactive } from 'vue'

const pageData = reactive({
  navBarHeight: 0
})

const handleToCreate = async () => {
  uni.navigateTo({ url: '/pages/createTraveler/selectTraveler' })
}
</script>

<style scoped lang="scss">
.page {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;

  .bg {
    position: absolute;
    top: var(--nav-bar-height);
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
  }

  .content {
    position: absolute;
    top: 1064rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .title {
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      font-size: 30px;
      line-height: 60px;
      text-align: center;
      color: #1e1e1e;
    }

    .sub-title {
      margin-top: 20rpx;
      font-size: 32rpx;
      color: #1e1e1e;
      text-align: center;
      line-height: 1.5;
    }

    .primary_button {
      margin-top: 60rpx;
    }
  }
}
</style>
