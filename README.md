# pet-weixin-mini

基于uniapp的Vue 3项目脚手架，支持多端开发（H5、微信小程序等）。

## 🚀 特性

- ✅ Vue 3 + TypeScript
- ✅ Composition API
- ✅ uniapp 多端支持
- ✅ VSCode 开发环境配置
- ✅ ESLint + Prettier 代码规范
- ✅ 组合式函数 (Composables)
- ✅ 工具函数库
- ✅ 示例页面和组件

## 📦 技术栈

- **框架**: uniapp + Vue 3
- **语言**: JavaScript
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier
- **开发工具**: VSCode

## 🛠️ 开发环境要求

- Node.js >= 16
- npm >= 8
- VSCode (推荐)

## 📋 推荐的VSCode插件

项目已配置插件推荐，首次打开项目时VSCode会提示安装：

- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag
- Path Intellisense

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式

```bash
# H5端开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# 支付宝小程序开发
npm run dev:mp-alipay

# 百度小程序开发
npm run dev:mp-baidu

# 字节跳动小程序开发
npm run dev:mp-toutiao

# QQ小程序开发
npm run dev:mp-qq
```

### 3. 构建生产版本

```bash
# 构建H5
npm run build:h5

# 构建微信小程序
npm run build:mp-weixin

# 构建其他平台
npm run build:mp-alipay
npm run build:mp-baidu
npm run build:mp-toutiao
npm run build:mp-qq
```

### 4. 代码检查和格式化

```bash
# ESLint检查
npm run lint

# Prettier格式化
npm run prettier
```

## 📁 项目结构

```
bux-weixin/
├── src/
│   ├── components/          # 公共组件
│   │   └── HelloWorld.vue   # 示例组件
│   ├── composables/         # 组合式函数
│   │   ├── useCounter.ts    # 计数器组合函数
│   │   └── useLoading.ts    # 加载状态管理
│   ├── pages/               # 页面
│   │   ├── index/           # 首页
│   │   ├── demo/            # Vue 3功能示例
│   │   └── composition/     # Composition API示例
│   ├── static/              # 静态资源
│   ├── utils/               # 工具函数
│   │   ├── index.ts         # 通用工具函数
│   │   └── request.ts       # 网络请求工具
│   ├── App.vue              # 应用入口组件
│   ├── main.ts              # 应用入口文件
│   ├── manifest.json        # 应用配置
│   ├── pages.json           # 页面路由配置
│   └── uni.scss             # 全局样式
├── .vscode/                 # VSCode配置
│   ├── extensions.json      # 插件推荐
│   ├── settings.json        # 工作区设置
│   ├── launch.json          # 调试配置
│   └── tasks.json           # 任务配置
├── .eslintrc.js             # ESLint配置
├── .prettierrc.js           # Prettier配置
├── tsconfig.json            # TypeScript配置
├── vite.config.ts           # Vite配置
└── package.json             # 项目配置
```

## 🎯 示例页面

### 1. 首页 (pages/index/index.vue)
- Vue 3基础用法展示
- 组件引用示例
- 计数器功能
- 实时时间显示

### 2. Vue 3功能示例 (pages/demo/demo.vue)
- 响应式数据 (ref, reactive)
- 计算属性 (computed)
- 列表渲染 (v-for)
- 条件渲染 (v-if)
- 事件处理

### 3. Composition API示例 (pages/composition/composition.vue)
- 组合式函数使用
- 生命周期钩子
- 侦听器 (watch)
- 响应式引用详解

## 🔧 组合式函数

### useCounter
计数器状态管理，包含：
- 响应式计数状态
- 计算属性（双倍值、奇偶判断）
- 操作方法（增加、减少、重置、设置值）

### useLoading
加载状态管理，包含：
- 加载状态控制
- 异步操作包装
- 自动状态管理

## 🛠️ 工具函数

### 通用工具 (utils/index.ts)
- 日期格式化
- 防抖/节流函数
- 深拷贝
- 随机字符串生成
- 本地存储封装

### 网络请求 (utils/request.ts)
- 统一请求封装
- 请求/响应拦截器
- 错误处理
- Token自动添加

## 🎨 开发规范

### 代码风格
- 使用 ESLint + Prettier 保证代码质量
- TypeScript 严格模式
- Vue 3 Composition API 优先
- 组件采用 PascalCase 命名
- 文件采用 kebab-case 命名

### 提交规范
建议使用 Conventional Commits 规范：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📱 多端适配

项目支持以下平台：
- H5
- 微信小程序
- 支付宝小程序
- 百度小程序
- 字节跳动小程序
- QQ小程序
- 快应用

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [uniapp](https://uniapp.dcloud.io/) - 跨平台应用开发框架
- [Vue 3](https://vuejs.org/) - 渐进式JavaScript框架
- [TypeScript](https://www.typescriptlang.org/) - JavaScript的超集
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
